<?php
require_once 'auth.php';
requireAdminLogin();

$currentAdmin = getCurrentAdmin();
$questionnaireId = intval($_GET['questionnaire_id'] ?? 0);

if ($questionnaireId <= 0) {
    header('Location: questionnaires.php');
    exit;
}

// Function to get questionnaire details
function getQuestionnaireDetails($id) {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("SELECT * FROM questionnaires WHERE id = :id");
        $stmt->execute(['id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        return null;
    }
}

// Function to get questions for questionnaire
function getQuestions($questionnaireId) {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("
            SELECT q.*, 
                   (SELECT COUNT(*) FROM questionnaire_responses_new WHERE question_id = q.id) as response_count
            FROM questionnaire_questions_new q 
            WHERE q.questionnaire_id = :questionnaire_id 
            ORDER BY q.question_order
        ");
        $stmt->execute(['questionnaire_id' => $questionnaireId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        return [];
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $conn = getConnection(DB_NAME_QUEST);
        
        if ($_POST['action'] === 'create' || $_POST['action'] === 'update') {
            $questionText = trim($_POST['question_text'] ?? '');
            $questionType = trim($_POST['question_type'] ?? '');
            $isRequired = isset($_POST['is_required']) ? 1 : 0;
            $options = null;
            
            if (empty($questionText)) {
                throw new Exception("Question text is required");
            }
            
            if (!in_array($questionType, ['multiple_choice', 'short_answer', 'long_answer'])) {
                throw new Exception("Invalid question type");
            }
            
            // Handle options for multiple choice
            if ($questionType === 'multiple_choice') {
                $optionsArray = array_filter(array_map('trim', explode("\n", $_POST['options'] ?? '')));
                if (empty($optionsArray)) {
                    throw new Exception("Options are required for multiple choice questions");
                }
                $options = json_encode($optionsArray);
            }
            
            if ($_POST['action'] === 'create') {
                // Get next order number
                $stmt = $conn->prepare("SELECT COALESCE(MAX(question_order), 0) + 1 as next_order FROM questionnaire_questions_new WHERE questionnaire_id = :questionnaire_id");
                $stmt->execute(['questionnaire_id' => $questionnaireId]);
                $nextOrder = $stmt->fetch(PDO::FETCH_ASSOC)['next_order'];
                
                $stmt = $conn->prepare("
                    INSERT INTO questionnaire_questions_new 
                    (questionnaire_id, question_order, question_text, question_type, options, is_required) 
                    VALUES (:questionnaire_id, :question_order, :question_text, :question_type, :options, :is_required)
                ");
                $stmt->execute([
                    'questionnaire_id' => $questionnaireId,
                    'question_order' => $nextOrder,
                    'question_text' => $questionText,
                    'question_type' => $questionType,
                    'options' => $options,
                    'is_required' => $isRequired
                ]);
                $message = "Question created successfully!";
            } else {
                $id = intval($_POST['id'] ?? 0);
                if ($id <= 0) throw new Exception("Invalid question ID");
                
                $stmt = $conn->prepare("
                    UPDATE questionnaire_questions_new 
                    SET question_text = :question_text, question_type = :question_type, 
                        options = :options, is_required = :is_required, updated_at = NOW() 
                    WHERE id = :id AND questionnaire_id = :questionnaire_id
                ");
                $stmt->execute([
                    'question_text' => $questionText,
                    'question_type' => $questionType,
                    'options' => $options,
                    'is_required' => $isRequired,
                    'id' => $id,
                    'questionnaire_id' => $questionnaireId
                ]);
                $message = "Question updated successfully!";
            }
            
            echo json_encode(['success' => true, 'message' => $message]);
            
        } elseif ($_POST['action'] === 'delete') {
            $id = intval($_POST['id'] ?? 0);
            if ($id <= 0) throw new Exception("Invalid question ID");
            
            // Check if question has responses
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM questionnaire_responses_new WHERE question_id = :id");
            $stmt->execute(['id' => $id]);
            $responseCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if ($responseCount > 0) {
                throw new Exception("Cannot delete question with existing responses.");
            }
            
            // Get the question order for reordering
            $stmt = $conn->prepare("SELECT question_order FROM questionnaire_questions_new WHERE id = :id");
            $stmt->execute(['id' => $id]);
            $deletedOrder = $stmt->fetch(PDO::FETCH_ASSOC)['question_order'];
            
            // Delete the question
            $stmt = $conn->prepare("DELETE FROM questionnaire_questions_new WHERE id = :id AND questionnaire_id = :questionnaire_id");
            $stmt->execute(['id' => $id, 'questionnaire_id' => $questionnaireId]);
            
            // Reorder remaining questions
            $stmt = $conn->prepare("
                UPDATE questionnaire_questions_new 
                SET question_order = question_order - 1 
                WHERE questionnaire_id = :questionnaire_id AND question_order > :deleted_order
            ");
            $stmt->execute(['questionnaire_id' => $questionnaireId, 'deleted_order' => $deletedOrder]);
            
            echo json_encode(['success' => true, 'message' => 'Question deleted successfully!']);
            
        } elseif ($_POST['action'] === 'reorder') {
            $questionIds = json_decode($_POST['question_ids'] ?? '[]', true);
            if (empty($questionIds)) {
                throw new Exception("No questions to reorder");
            }
            
            $conn->beginTransaction();
            
            foreach ($questionIds as $index => $questionId) {
                $stmt = $conn->prepare("
                    UPDATE questionnaire_questions_new 
                    SET question_order = :order 
                    WHERE id = :id AND questionnaire_id = :questionnaire_id
                ");
                $stmt->execute([
                    'order' => $index + 1,
                    'id' => $questionId,
                    'questionnaire_id' => $questionnaireId
                ]);
            }
            
            $conn->commit();
            echo json_encode(['success' => true, 'message' => 'Questions reordered successfully!']);
        }
        
    } catch (Exception $e) {
        if (isset($conn) && $conn->inTransaction()) {
            $conn->rollBack();
        }
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

$questionnaire = getQuestionnaireDetails($questionnaireId);
if (!$questionnaire) {
    header('Location: questionnaires.php');
    exit;
}

$questions = getQuestions($questionnaireId);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Questions - <?php echo htmlspecialchars($questionnaire['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-brand img { max-height: 40px; }
        .question-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 10px;
            cursor: move;
        }
        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .question-type-badge {
            font-size: 0.8em;
        }
        .sortable-ghost {
            opacity: 0.4;
        }
        .drag-handle {
            cursor: move;
            color: #6c757d;
        }
        .drag-handle:hover {
            color: #495057;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <img src="../ihcslogo.svg" alt="IHCS Logo" class="me-2">
                IHCS Admin Portal
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($currentAdmin['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                        <li><a class="dropdown-item" href="questionnaires.php"><i class="fas fa-clipboard-list me-2"></i>Questionnaires</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="auth.php?action=logout"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="questionnaires.php">Questionnaires</a></li>
                        <li class="breadcrumb-item active"><?php echo htmlspecialchars($questionnaire['title']); ?></li>
                    </ol>
                </nav>
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-question-circle me-2"></i>Manage Questions</h2>
                        <p class="text-muted mb-0">
                            <?php echo htmlspecialchars($questionnaire['title']); ?>
                            <?php if ($questionnaire['is_active']): ?>
                                <span class="badge bg-success ms-2">Active</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#questionModal">
                        <i class="fas fa-plus me-1"></i>Add Question
                    </button>
                </div>
            </div>
        </div>

        <!-- Message Area -->
        <div id="messageArea"></div>

        <!-- Questions List -->
        <div class="row">
            <div class="col-12">
                <?php if (empty($questions)): ?>
                    <div class="card text-center">
                        <div class="card-body py-5">
                            <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                            <h5>No Questions Found</h5>
                            <p class="text-muted">Add your first question to get started.</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#questionModal">
                                <i class="fas fa-plus me-1"></i>Add Question
                            </button>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Questions (<?php echo count($questions); ?>)</h5>
                                <small class="text-muted">
                                    <i class="fas fa-arrows-alt me-1"></i>Drag to reorder
                                </small>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="questionsList">
                                <?php foreach ($questions as $question): ?>
                                    <div class="question-item border-bottom p-3" data-question-id="<?php echo $question['id']; ?>">
                                        <div class="d-flex align-items-start">
                                            <div class="drag-handle me-3 mt-1">
                                                <i class="fas fa-grip-vertical"></i>
                                            </div>
                                            
                                            <div class="flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <div>
                                                        <span class="badge bg-secondary me-2">Q<?php echo $question['question_order']; ?></span>
                                                        <span class="badge bg-info question-type-badge me-2">
                                                            <?php echo ucfirst(str_replace('_', ' ', $question['question_type'])); ?>
                                                        </span>
                                                        <?php if ($question['is_required']): ?>
                                                            <span class="badge bg-danger question-type-badge">Required</span>
                                                        <?php endif; ?>
                                                    </div>
                                                    
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" 
                                                                onclick="editQuestion(<?php echo htmlspecialchars(json_encode($question)); ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <?php if ($question['response_count'] == 0): ?>
                                                            <button class="btn btn-outline-danger" 
                                                                    onclick="deleteQuestion(<?php echo $question['id']; ?>)">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                
                                                <h6 class="mb-2"><?php echo htmlspecialchars($question['question_text']); ?></h6>
                                                
                                                <?php if ($question['question_type'] === 'multiple_choice' && $question['options']): ?>
                                                    <div class="text-muted small">
                                                        <strong>Options:</strong>
                                                        <?php 
                                                        $options = json_decode($question['options'], true);
                                                        echo htmlspecialchars(implode(', ', $options));
                                                        ?>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if ($question['response_count'] > 0): ?>
                                                    <div class="text-muted small mt-1">
                                                        <i class="fas fa-users me-1"></i><?php echo $question['response_count']; ?> responses
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Question Modal -->
    <div class="modal fade" id="questionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Add New Question</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="questionForm">
                    <div class="modal-body">
                        <input type="hidden" id="questionId" name="id">
                        
                        <div class="mb-3">
                            <label for="questionText" class="form-label">Question Text <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="questionText" name="question_text" rows="3" required></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="questionType" class="form-label">Question Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="questionType" name="question_type" required>
                                        <option value="">Select type...</option>
                                        <option value="multiple_choice">Multiple Choice</option>
                                        <option value="short_answer">Short Answer</option>
                                        <option value="long_answer">Long Answer</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isRequired" name="is_required" checked>
                                        <label class="form-check-label" for="isRequired">
                                            Required
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="optionsContainer" style="display: none;">
                            <label for="options" class="form-label">Options (one per line) <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="options" name="options" rows="4" 
                                      placeholder="Option 1&#10;Option 2&#10;Option 3"></textarea>
                            <div class="form-text">Enter each option on a new line</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="saveBtn">Add Question</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
    const messageArea = document.getElementById('messageArea');
    const questionModal = new bootstrap.Modal(document.getElementById('questionModal'));
    const questionForm = document.getElementById('questionForm');
    const questionType = document.getElementById('questionType');
    const optionsContainer = document.getElementById('optionsContainer');

    // Initialize sortable
    const questionsList = document.getElementById('questionsList');
    if (questionsList) {
        new Sortable(questionsList, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            handle: '.drag-handle',
            onEnd: function(evt) {
                const questionIds = Array.from(questionsList.children).map(item => 
                    item.getAttribute('data-question-id')
                );
                
                const formData = new FormData();
                formData.append('action', 'reorder');
                formData.append('question_ids', JSON.stringify(questionIds));
                
                fetch('questions.php?questionnaire_id=<?php echo $questionnaireId; ?>', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showMessage(data.message, 'danger');
                    }
                });
            }
        });
    }

    // Show/hide options based on question type
    questionType.addEventListener('change', function() {
        if (this.value === 'multiple_choice') {
            optionsContainer.style.display = 'block';
            document.getElementById('options').required = true;
        } else {
            optionsContainer.style.display = 'none';
            document.getElementById('options').required = false;
        }
    });

    // Handle form submission
    questionForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(questionForm);
        const isEdit = document.getElementById('questionId').value !== '';
        formData.append('action', isEdit ? 'update' : 'create');
        
        const saveBtn = document.getElementById('saveBtn');
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Saving...';
        
        fetch('questions.php?questionnaire_id=<?php echo $questionnaireId; ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                questionModal.hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'danger');
            }
        })
        .catch(error => {
            showMessage('An error occurred. Please try again.', 'danger');
        })
        .finally(() => {
            saveBtn.disabled = false;
            saveBtn.innerHTML = isEdit ? 'Update Question' : 'Add Question';
        });
    });

    // Reset modal when hidden
    document.getElementById('questionModal').addEventListener('hidden.bs.modal', function() {
        questionForm.reset();
        document.getElementById('questionId').value = '';
        document.getElementById('modalTitle').textContent = 'Add New Question';
        document.getElementById('saveBtn').textContent = 'Add Question';
        optionsContainer.style.display = 'none';
        document.getElementById('options').required = false;
    });

    function editQuestion(question) {
        document.getElementById('questionId').value = question.id;
        document.getElementById('questionText').value = question.question_text;
        document.getElementById('questionType').value = question.question_type;
        document.getElementById('isRequired').checked = question.is_required == 1;
        
        if (question.question_type === 'multiple_choice' && question.options) {
            const options = JSON.parse(question.options);
            document.getElementById('options').value = options.join('\n');
            optionsContainer.style.display = 'block';
            document.getElementById('options').required = true;
        }
        
        document.getElementById('modalTitle').textContent = 'Edit Question';
        document.getElementById('saveBtn').textContent = 'Update Question';
        questionModal.show();
    }

    function deleteQuestion(id) {
        if (confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
            const formData = new FormData();
            formData.append('action', 'delete');
            formData.append('id', id);
            
            fetch('questions.php?questionnaire_id=<?php echo $questionnaireId; ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage(data.message, 'danger');
                }
            });
        }
    }

    function showMessage(message, type) {
        messageArea.innerHTML = `<div class="alert alert-${type} alert-dismissible fade show">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
        window.scrollTo(0, 0);
    }
    </script>
</body>
</html>
