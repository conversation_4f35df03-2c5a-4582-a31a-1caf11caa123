<?php
/**
 * Questionnaire Response Export Script
 * Exports questionnaire responses to CSV format
 */

// Database configuration (same as index.php)
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', 'laijingyue1984');
define('DB_NAME_QUEST', 'ihcs_quest');

// Function to get database connection
function getConnection($dbName) {
    try {
        $conn = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . $dbName, 
            DB_USER, 
            DB_PASS
        );
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

// Function to get all responses with question details
function getAllResponsesWithQuestions() {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        
        $sql = "SELECT 
                    r.staff_id,
                    r.staff_name,
                    q.question_order,
                    q.question_text,
                    q.question_type,
                    r.response_text,
                    r.created_at,
                    r.updated_at,
                    s.is_completed,
                    s.completed_at
                FROM questionnaire_responses r
                JOIN questionnaire_questions q ON r.question_id = q.id
                LEFT JOIN questionnaire_sessions s ON r.staff_id = s.staff_id
                ORDER BY r.staff_id, q.question_order";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        throw new Exception("Failed to fetch responses: " . $e->getMessage());
    }
}

// Function to get completion statistics
function getCompletionStats() {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        
        $sql = "SELECT 
                    COUNT(*) as total_sessions,
                    SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed_sessions,
                    SUM(CASE WHEN consent_given = 1 THEN 1 ELSE 0 END) as consented_sessions,
                    COUNT(DISTINCT r.staff_id) as staff_with_responses
                FROM questionnaire_sessions s
                LEFT JOIN questionnaire_responses r ON s.staff_id = r.staff_id";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        throw new Exception("Failed to fetch completion stats: " . $e->getMessage());
    }
}

// Function to export responses to CSV
function exportResponsesToCSV($filename = null) {
    if (!$filename) {
        $filename = 'questionnaire_responses_' . date('Y-m-d_H-i-s') . '.csv';
    }
    
    try {
        $responses = getAllResponsesWithQuestions();
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        // Open output stream
        $output = fopen('php://output', 'w');
        
        // Write CSV header
        fputcsv($output, [
            'Staff ID',
            'Staff Name',
            'Question Order',
            'Question Text',
            'Question Type',
            'Response',
            'Response Created',
            'Response Updated',
            'Questionnaire Completed',
            'Completion Date'
        ]);
        
        // Write data rows
        foreach ($responses as $response) {
            fputcsv($output, [
                $response['staff_id'],
                $response['staff_name'],
                $response['question_order'],
                $response['question_text'],
                $response['question_type'],
                $response['response_text'],
                $response['created_at'],
                $response['updated_at'],
                $response['is_completed'] ? 'Yes' : 'No',
                $response['completed_at'] ?: 'Not completed'
            ]);
        }
        
        fclose($output);
        exit;
        
    } catch (Exception $e) {
        die("Error exporting responses: " . $e->getMessage());
    }
}

// Function to export summary statistics to CSV
function exportSummaryToCSV($filename = null) {
    if (!$filename) {
        $filename = 'questionnaire_summary_' . date('Y-m-d_H-i-s') . '.csv';
    }
    
    try {
        $conn = getConnection(DB_NAME_QUEST);
        
        // Get question-wise response summary
        $sql = "SELECT 
                    q.question_order,
                    q.question_text,
                    q.question_type,
                    COUNT(r.response_text) as response_count,
                    r.response_text,
                    COUNT(*) as frequency
                FROM questionnaire_questions q
                LEFT JOIN questionnaire_responses r ON q.id = r.question_id
                WHERE r.response_text IS NOT NULL
                GROUP BY q.id, r.response_text
                ORDER BY q.question_order, frequency DESC";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $summary = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        // Open output stream
        $output = fopen('php://output', 'w');
        
        // Write CSV header
        fputcsv($output, [
            'Question Order',
            'Question Text',
            'Question Type',
            'Response',
            'Frequency',
            'Percentage'
        ]);
        
        // Calculate percentages and write data
        $currentQuestion = null;
        $questionTotal = 0;
        $questionResponses = [];
        
        foreach ($summary as $row) {
            if ($currentQuestion !== $row['question_order']) {
                // Write previous question's data with percentages
                if ($currentQuestion !== null) {
                    foreach ($questionResponses as $resp) {
                        $percentage = $questionTotal > 0 ? round(($resp['frequency'] / $questionTotal) * 100, 2) : 0;
                        fputcsv($output, [
                            $resp['question_order'],
                            $resp['question_text'],
                            $resp['question_type'],
                            $resp['response_text'],
                            $resp['frequency'],
                            $percentage . '%'
                        ]);
                    }
                }
                
                // Reset for new question
                $currentQuestion = $row['question_order'];
                $questionTotal = 0;
                $questionResponses = [];
                
                // Calculate total for this question
                $totalSql = "SELECT COUNT(*) as total FROM questionnaire_responses r 
                           JOIN questionnaire_questions q ON r.question_id = q.id 
                           WHERE q.question_order = :order";
                $totalStmt = $conn->prepare($totalSql);
                $totalStmt->execute(['order' => $currentQuestion]);
                $questionTotal = $totalStmt->fetch(PDO::FETCH_ASSOC)['total'];
            }
            
            $questionResponses[] = $row;
        }
        
        // Write last question's data
        if (!empty($questionResponses)) {
            foreach ($questionResponses as $resp) {
                $percentage = $questionTotal > 0 ? round(($resp['frequency'] / $questionTotal) * 100, 2) : 0;
                fputcsv($output, [
                    $resp['question_order'],
                    $resp['question_text'],
                    $resp['question_type'],
                    $resp['response_text'],
                    $resp['frequency'],
                    $percentage . '%'
                ]);
            }
        }
        
        fclose($output);
        exit;
        
    } catch (Exception $e) {
        die("Error exporting summary: " . $e->getMessage());
    }
}

// Handle different export types
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'export_responses':
            exportResponsesToCSV();
            break;
        case 'export_summary':
            exportSummaryToCSV();
            break;
        default:
            die("Invalid action specified");
    }
}

// If no action specified, show the interface
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Questionnaire Data Export</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 800px; }
        .card { margin-top: 50px; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-0">Questionnaire Data Export</h4>
            </div>
            <div class="card-body">
                <?php
                try {
                    $stats = getCompletionStats();
                    echo "<div class='alert alert-info'>";
                    echo "<h6>Current Statistics:</h6>";
                    echo "<ul class='mb-0'>";
                    echo "<li>Total Sessions: " . $stats['total_sessions'] . "</li>";
                    echo "<li>Completed Questionnaires: " . $stats['completed_sessions'] . "</li>";
                    echo "<li>Staff with Responses: " . $stats['staff_with_responses'] . "</li>";
                    echo "<li>Consented Sessions: " . $stats['consented_sessions'] . "</li>";
                    echo "</ul>";
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error loading statistics: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <h5 class="card-title">Export All Responses</h5>
                                <p class="card-text">Download detailed responses for each staff member and question.</p>
                                <a href="?action=export_responses" class="btn btn-primary">
                                    <i class="fas fa-download"></i> Download Responses CSV
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <h5 class="card-title">Export Summary Statistics</h5>
                                <p class="card-text">Download response frequency and percentage analysis by question.</p>
                                <a href="?action=export_summary" class="btn btn-success">
                                    <i class="fas fa-chart-bar"></i> Download Summary CSV
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Questionnaire
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
