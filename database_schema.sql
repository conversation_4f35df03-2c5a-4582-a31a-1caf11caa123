-- Database schema for online questionnaire system
-- Use the existing ihcs_quest database

USE ihcs_quest;

-- Table to store questionnaire questions
CREATE TABLE IF NOT EXISTS questionnaire_questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    question_order INT NOT NULL,
    question_text TEXT NOT NULL,
    question_type ENUM('multiple_choice', 'short_answer', 'long_answer') NOT NULL,
    options JSON NULL, -- For multiple choice questions, store options as JSON array
    is_required BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_order (question_order)
);

-- Table to store user responses
CREATE TABLE IF NOT EXISTS questionnaire_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    staff_id VARCHAR(50) NOT NULL,
    staff_name VARCHAR(255) NOT NULL,
    question_id INT NOT NULL,
    response_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (question_id) REFERENCES questionnaire_questions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_staff_question (staff_id, question_id)
);

-- Table to track questionnaire completion status
CREATE TABLE IF NOT EXISTS questionnaire_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    staff_id VARCHAR(50) NOT NULL UNIQUE,
    staff_name VARCHAR(255) NOT NULL,
    consent_given BOOLEAN DEFAULT FALSE,
    current_question INT DEFAULT 1,
    is_completed BOOLEAN DEFAULT FALSE,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert sample questions
INSERT INTO questionnaire_questions (question_order, question_text, question_type, options, is_required) VALUES
(1, 'What is your current employment status?', 'multiple_choice', '["Full-time", "Part-time", "Casual", "Contract"]', TRUE),
(2, 'How many years have you been working with IHCS?', 'multiple_choice', '["Less than 1 year", "1-2 years", "3-5 years", "6-10 years", "More than 10 years"]', TRUE),
(3, 'What department do you work in?', 'short_answer', NULL, TRUE),
(4, 'How satisfied are you with your current benefits package?', 'multiple_choice', '["Very satisfied", "Satisfied", "Neutral", "Dissatisfied", "Very dissatisfied"]', TRUE),
(5, 'What additional benefits would you like to see offered? Please explain in detail.', 'long_answer', NULL, TRUE),
(6, 'Would you recommend IHCS as an employer to others?', 'multiple_choice', '["Definitely yes", "Probably yes", "Not sure", "Probably no", "Definitely no"]', TRUE),
(7, 'Any additional comments or suggestions?', 'long_answer', NULL, FALSE);

-- Create indexes for better performance
CREATE INDEX idx_staff_id ON questionnaire_responses(staff_id);
CREATE INDEX idx_question_order ON questionnaire_questions(question_order);
CREATE INDEX idx_session_staff ON questionnaire_sessions(staff_id);
