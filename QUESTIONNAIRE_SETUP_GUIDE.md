# IHCS Online Questionnaire System - Setup Guide

## Overview
The index.php file has been completely transformed into a multi-page online questionnaire system with all the requested features.

## Features Implemented

### 1. ✅ Consent Management (No Database Recording)
- Users must tick the consent checkbox to proceed
- Consent is validated but NOT stored in database (as requested)
- Uses reCAPTCHA v3 for security

### 2. ✅ Staff ID Verification
- Verifies `$staffId` ($_POST['key']) against the `ihcs_pays.staff` table
- Users cannot proceed without valid staff ID
- Staff name is retrieved and used throughout the system

### 3. ✅ Multi-Question Support
- Supports multiple question types:
  - Multiple choice (radio buttons)
  - Short answer (text input)
  - Long answer (textarea)
- Questions are stored in database with configurable options

### 4. ✅ One Question Per Page
- Each question is displayed on a separate page
- Navigation between questions with Previous/Next buttons
- Progress bar shows completion status
- Current question tracking in session

### 5. ✅ Response Storage
- All user responses stored in `questionnaire_responses` table
- Automatic handling of new responses and updates
- Unique constraint prevents duplicate responses per user/question

### 6. ✅ Response Update Capability
- Users can navigate back to previous questions
- Existing responses are pre-filled in forms
- Updates overwrite previous responses automatically

## Database Setup

### Step 1: Run the Database Schema
Execute the `database_schema.sql` file to create the required tables:

```sql
-- Run this in your MySQL database
source database_schema.sql;
```

### Step 2: Tables Created
1. **questionnaire_questions** - Stores all questions
2. **questionnaire_responses** - Stores user responses
3. **questionnaire_sessions** - Tracks user progress and completion

### Step 3: Sample Questions Included
The schema includes 7 sample questions covering:
- Employment status
- Years of service
- Department
- Benefits satisfaction
- Additional benefits suggestions
- Employer recommendation
- Additional comments

## How It Works

### User Flow
1. **Access**: User clicks link with `?key=STAFF_ID`
2. **Verification**: System verifies staff ID exists
3. **Consent**: User must agree to consent (not stored in DB)
4. **Questionnaire**: User answers questions one by one
5. **Navigation**: Can go back/forward between questions
6. **Completion**: System tracks completion status

### Session Management
- Each user has a session record tracking:
  - Consent status
  - Current question position
  - Completion status
  - Timestamps

### Response Handling
- Responses are saved immediately when navigating
- Required questions must be answered to proceed
- Previous responses are loaded when returning to questions

## File Structure

### Updated Files
- `index.php` - Complete questionnaire system
- `database_schema.sql` - Database setup script
- `QUESTIONNAIRE_SETUP_GUIDE.md` - This guide

### Key Functions Added
- `getQuestionnaireSession()` - Get user session
- `createOrUpdateSession()` - Manage user sessions
- `getAllQuestions()` - Get all questions
- `getQuestionByOrder()` - Get specific question
- `getUserResponse()` - Get user's response
- `saveUserResponse()` - Save/update responses
- `markQuestionnaireCompleted()` - Mark completion

## Security Features
- reCAPTCHA v3 integration
- SQL injection protection with prepared statements
- XSS protection with htmlspecialchars()
- Session-based state management
- Staff ID verification

## Customization

### Adding Questions
Add questions directly to the database:
```sql
INSERT INTO questionnaire_questions 
(question_order, question_text, question_type, options, is_required) 
VALUES 
(8, 'Your new question?', 'multiple_choice', '["Option 1", "Option 2"]', TRUE);
```

### Question Types
- `multiple_choice` - Radio buttons (requires options JSON)
- `short_answer` - Single line text input
- `long_answer` - Multi-line textarea

### Styling
The system uses Bootstrap 5 with custom CSS for:
- Progress bars
- Question styling
- Navigation buttons
- Responsive design

## Testing Checklist
- [ ] Database tables created successfully
- [ ] Valid staff ID allows access
- [ ] Invalid staff ID shows error
- [ ] Consent checkbox required to proceed
- [ ] Questions display correctly by type
- [ ] Navigation between questions works
- [ ] Responses are saved and retrieved
- [ ] Progress bar updates correctly
- [ ] Completion status tracked
- [ ] Users can update previous responses

## Support
For any issues or customizations needed, refer to the code comments in index.php or contact the development team.
