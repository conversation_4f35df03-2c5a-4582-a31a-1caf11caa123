<?php
require_once 'auth.php';
requireAdminLogin();

$currentAdmin = getCurrentAdmin();

// Function to get all questionnaires
function getAllQuestionnaires() {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("
            SELECT q.*, a.full_name as created_by_name,
                   (SELECT COUNT(*) FROM questionnaire_questions_new WHERE questionnaire_id = q.id) as question_count,
                   (SELECT COUNT(DISTINCT staff_id) FROM questionnaire_responses_new WHERE questionnaire_id = q.id) as response_count
            FROM questionnaires q
            LEFT JOIN admin_users a ON q.created_by = a.id
            ORDER BY q.created_at DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        return [];
    }
}

// Function to get questionnaire by ID
function getQuestionnaireById($id) {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("SELECT * FROM questionnaires WHERE id = :id");
        $stmt->execute(['id' => $id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        return null;
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $conn = getConnection(DB_NAME_QUEST);
        
        if ($_POST['action'] === 'create' || $_POST['action'] === 'update') {
            $title = trim($_POST['title'] ?? '');
            $description = trim($_POST['description'] ?? '');
            
            if (empty($title)) {
                throw new Exception("Title is required");
            }
            
            if ($_POST['action'] === 'create') {
                $stmt = $conn->prepare("INSERT INTO questionnaires (title, description, created_by) VALUES (:title, :description, :created_by)");
                $stmt->execute([
                    'title' => $title,
                    'description' => $description,
                    'created_by' => $currentAdmin['id']
                ]);
                $message = "Questionnaire created successfully!";
            } else {
                $id = intval($_POST['id'] ?? 0);
                if ($id <= 0) throw new Exception("Invalid questionnaire ID");
                
                $stmt = $conn->prepare("UPDATE questionnaires SET title = :title, description = :description, updated_at = NOW() WHERE id = :id");
                $stmt->execute([
                    'title' => $title,
                    'description' => $description,
                    'id' => $id
                ]);
                $message = "Questionnaire updated successfully!";
            }
            
            echo json_encode(['success' => true, 'message' => $message]);
            
        } elseif ($_POST['action'] === 'activate') {
            $id = intval($_POST['id'] ?? 0);
            if ($id <= 0) throw new Exception("Invalid questionnaire ID");
            
            // Deactivate all questionnaires first
            $stmt = $conn->prepare("UPDATE questionnaires SET is_active = 0");
            $stmt->execute();
            
            // Activate the selected one
            $stmt = $conn->prepare("UPDATE questionnaires SET is_active = 1, updated_at = NOW() WHERE id = :id");
            $stmt->execute(['id' => $id]);
            
            echo json_encode(['success' => true, 'message' => 'Questionnaire activated successfully!']);
            
        } elseif ($_POST['action'] === 'delete') {
            $id = intval($_POST['id'] ?? 0);
            if ($id <= 0) throw new Exception("Invalid questionnaire ID");
            
            // Check if questionnaire has responses
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM questionnaire_responses_new WHERE questionnaire_id = :id");
            $stmt->execute(['id' => $id]);
            $responseCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if ($responseCount > 0) {
                throw new Exception("Cannot delete questionnaire with existing responses. Archive it instead.");
            }
            
            $stmt = $conn->prepare("DELETE FROM questionnaires WHERE id = :id");
            $stmt->execute(['id' => $id]);
            
            echo json_encode(['success' => true, 'message' => 'Questionnaire deleted successfully!']);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

$questionnaires = getAllQuestionnaires();
$editQuestionnaire = null;

// Check if editing
if (isset($_GET['edit'])) {
    $editQuestionnaire = getQuestionnaireById(intval($_GET['edit']));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Questionnaires - IHCS Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-brand img { max-height: 40px; }
        .questionnaire-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 10px;
        }
        .questionnaire-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .active-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .stats-badge {
            font-size: 0.8em;
            margin: 2px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <img src="../ihcslogo.svg" alt="IHCS Logo" class="me-2">
                IHCS Admin Portal
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($currentAdmin['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="auth.php?action=logout"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-clipboard-list me-2"></i>Manage Questionnaires</h2>
                        <p class="text-muted">Create, edit, and manage your questionnaires</p>
                    </div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#questionnaireModal">
                        <i class="fas fa-plus me-1"></i>Create New Questionnaire
                    </button>
                </div>
            </div>
        </div>

        <!-- Message Area -->
        <div id="messageArea"></div>

        <!-- Questionnaires Grid -->
        <div class="row">
            <?php if (empty($questionnaires)): ?>
                <div class="col-12">
                    <div class="card text-center">
                        <div class="card-body py-5">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h5>No Questionnaires Found</h5>
                            <p class="text-muted">Create your first questionnaire to get started.</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#questionnaireModal">
                                <i class="fas fa-plus me-1"></i>Create Questionnaire
                            </button>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($questionnaires as $questionnaire): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card questionnaire-card h-100 position-relative">
                            <?php if ($questionnaire['is_active']): ?>
                                <span class="badge bg-success active-badge">Active</span>
                            <?php endif; ?>
                            
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($questionnaire['title']); ?></h5>
                                <p class="card-text text-muted"><?php echo htmlspecialchars($questionnaire['description'] ?: 'No description'); ?></p>
                                
                                <div class="mb-3">
                                    <span class="badge bg-info stats-badge">
                                        <i class="fas fa-question-circle me-1"></i><?php echo $questionnaire['question_count']; ?> Questions
                                    </span>
                                    <span class="badge bg-secondary stats-badge">
                                        <i class="fas fa-users me-1"></i><?php echo $questionnaire['response_count']; ?> Responses
                                    </span>
                                </div>
                                
                                <small class="text-muted">
                                    Created by <?php echo htmlspecialchars($questionnaire['created_by_name'] ?: 'Unknown'); ?><br>
                                    <?php echo date('M j, Y', strtotime($questionnaire['created_at'])); ?>
                                </small>
                            </div>
                            
                            <div class="card-footer bg-transparent">
                                <div class="btn-group w-100" role="group">
                                    <a href="questions.php?questionnaire_id=<?php echo $questionnaire['id']; ?>" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-edit me-1"></i>Questions
                                    </a>
                                    
                                    <?php if (!$questionnaire['is_active']): ?>
                                        <button class="btn btn-outline-success btn-sm" 
                                                onclick="activateQuestionnaire(<?php echo $questionnaire['id']; ?>)">
                                            <i class="fas fa-play me-1"></i>Activate
                                        </button>
                                    <?php endif; ?>
                                    
                                    <button class="btn btn-outline-secondary btn-sm" 
                                            onclick="editQuestionnaire(<?php echo $questionnaire['id']; ?>, '<?php echo htmlspecialchars($questionnaire['title'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($questionnaire['description'], ENT_QUOTES); ?>')">
                                        <i class="fas fa-pencil-alt"></i>
                                    </button>
                                    
                                    <?php if ($questionnaire['response_count'] == 0): ?>
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteQuestionnaire(<?php echo $questionnaire['id']; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Questionnaire Modal -->
    <div class="modal fade" id="questionnaireModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Create New Questionnaire</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="questionnaireForm">
                    <div class="modal-body">
                        <input type="hidden" id="questionnaireId" name="id">
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="Brief description of this questionnaire"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="saveBtn">Create Questionnaire</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    const messageArea = document.getElementById('messageArea');
    const questionnaireModal = new bootstrap.Modal(document.getElementById('questionnaireModal'));
    const questionnaireForm = document.getElementById('questionnaireForm');

    // Handle form submission
    questionnaireForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(questionnaireForm);
        const isEdit = document.getElementById('questionnaireId').value !== '';
        formData.append('action', isEdit ? 'update' : 'create');
        
        const saveBtn = document.getElementById('saveBtn');
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Saving...';
        
        fetch('questionnaires.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                questionnaireModal.hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showMessage(data.message, 'danger');
            }
        })
        .catch(error => {
            showMessage('An error occurred. Please try again.', 'danger');
        })
        .finally(() => {
            saveBtn.disabled = false;
            saveBtn.innerHTML = isEdit ? 'Update Questionnaire' : 'Create Questionnaire';
        });
    });

    // Reset modal when hidden
    document.getElementById('questionnaireModal').addEventListener('hidden.bs.modal', function() {
        questionnaireForm.reset();
        document.getElementById('questionnaireId').value = '';
        document.getElementById('modalTitle').textContent = 'Create New Questionnaire';
        document.getElementById('saveBtn').textContent = 'Create Questionnaire';
    });

    function editQuestionnaire(id, title, description) {
        document.getElementById('questionnaireId').value = id;
        document.getElementById('title').value = title;
        document.getElementById('description').value = description;
        document.getElementById('modalTitle').textContent = 'Edit Questionnaire';
        document.getElementById('saveBtn').textContent = 'Update Questionnaire';
        questionnaireModal.show();
    }

    function activateQuestionnaire(id) {
        if (confirm('Are you sure you want to activate this questionnaire? This will deactivate the current active questionnaire.')) {
            const formData = new FormData();
            formData.append('action', 'activate');
            formData.append('id', id);
            
            fetch('questionnaires.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage(data.message, 'danger');
                }
            });
        }
    }

    function deleteQuestionnaire(id) {
        if (confirm('Are you sure you want to delete this questionnaire? This action cannot be undone.')) {
            const formData = new FormData();
            formData.append('action', 'delete');
            formData.append('id', id);
            
            fetch('questionnaires.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showMessage(data.message, 'danger');
                }
            });
        }
    }

    function showMessage(message, type) {
        messageArea.innerHTML = `<div class="alert alert-${type} alert-dismissible fade show">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;
        window.scrollTo(0, 0);
    }

    <?php if ($editQuestionnaire): ?>
    // Auto-open edit modal if edit parameter is present
    editQuestionnaire(
        <?php echo $editQuestionnaire['id']; ?>, 
        '<?php echo htmlspecialchars($editQuestionnaire['title'], ENT_QUOTES); ?>', 
        '<?php echo htmlspecialchars($editQuestionnaire['description'], ENT_QUOTES); ?>'
    );
    <?php endif; ?>
    </script>
</body>
</html>
