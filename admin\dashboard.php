<?php
require_once 'auth.php';
requireAdminLogin();

$currentAdmin = getCurrentAdmin();

// Function to get questionnaire statistics
function getQuestionnaireStats() {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        
        // Get total questionnaires
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM questionnaires");
        $stmt->execute();
        $totalQuestionnaires = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Get active questionnaire
        $stmt = $conn->prepare("SELECT id, title FROM questionnaires WHERE is_active = 1 LIMIT 1");
        $stmt->execute();
        $activeQuestionnaire = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get response statistics for active questionnaire
        $responseStats = ['total_responses' => 0, 'completed_sessions' => 0, 'unique_respondents' => 0];
        if ($activeQuestionnaire) {
            // Check if new tables exist, fallback to old tables
            $stmt = $conn->prepare("SHOW TABLES LIKE 'questionnaire_responses_new'");
            $stmt->execute();
            $useNewTables = $stmt->rowCount() > 0;

            if ($useNewTables) {
                $stmt = $conn->prepare("
                    SELECT
                        COUNT(DISTINCT r.staff_id) as unique_respondents,
                        COUNT(r.id) as total_responses,
                        SUM(CASE WHEN s.is_completed = 1 THEN 1 ELSE 0 END) as completed_sessions
                    FROM questionnaire_responses_new r
                    LEFT JOIN questionnaire_sessions_new s ON r.questionnaire_id = s.questionnaire_id AND r.staff_id = s.staff_id
                    WHERE r.questionnaire_id = :questionnaire_id
                ");
            } else {
                // Fallback to old table structure
                $stmt = $conn->prepare("
                    SELECT
                        COUNT(DISTINCT r.staff_id) as unique_respondents,
                        COUNT(r.id) as total_responses,
                        SUM(CASE WHEN s.is_completed = 1 THEN 1 ELSE 0 END) as completed_sessions
                    FROM questionnaire_responses r
                    LEFT JOIN questionnaire_sessions s ON r.staff_id = s.staff_id
                ");
            }
            $stmt->execute($useNewTables ? ['questionnaire_id' => $activeQuestionnaire['id']] : []);
            $responseStats = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        return [
            'total_questionnaires' => $totalQuestionnaires,
            'active_questionnaire' => $activeQuestionnaire,
            'response_stats' => $responseStats
        ];
        
    } catch(PDOException $e) {
        return [
            'total_questionnaires' => 0,
            'active_questionnaire' => null,
            'response_stats' => ['total_responses' => 0, 'completed_sessions' => 0, 'unique_respondents' => 0]
        ];
    }
}

$stats = getQuestionnaireStats();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - IHCS Questionnaire System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-brand img { max-height: 40px; }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            transition: transform 0.3s ease;
        }
        .stats-card:hover { transform: translateY(-5px); }
        .stats-number { font-size: 2.5rem; font-weight: bold; }
        .quick-action-card {
            border: none;
            border-radius: 15px;
            transition: all 0.3s ease;
            height: 100%;
        }
        .quick-action-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .action-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <img src="../ihcslogo.svg" alt="IHCS Logo" class="me-2">
                IHCS Admin Portal
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($currentAdmin['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="auth.php?action=logout"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
                <p class="text-muted">Welcome back, <?php echo htmlspecialchars($currentAdmin['full_name']); ?>!</p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                        <div class="stats-number"><?php echo $stats['total_questionnaires']; ?></div>
                        <div>Total Questionnaires</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <div class="stats-number"><?php echo $stats['response_stats']['unique_respondents']; ?></div>
                        <div>Unique Respondents</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-comments fa-2x mb-2"></i>
                        <div class="stats-number"><?php echo $stats['response_stats']['total_responses']; ?></div>
                        <div>Total Responses</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <div class="stats-number"><?php echo $stats['response_stats']['completed_sessions']; ?></div>
                        <div>Completed</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Questionnaire Info -->
        <div class="row mb-4">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-star me-2"></i>Active Questionnaire</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($stats['active_questionnaire']): ?>
                            <h6 class="text-primary"><?php echo htmlspecialchars($stats['active_questionnaire']['title']); ?></h6>
                            <p class="text-muted mb-2">This questionnaire is currently active and available to users.</p>
                            <div class="btn-group">
                                <a href="questionnaires.php?edit=<?php echo $stats['active_questionnaire']['id']; ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </a>
                                <a href="questions.php?questionnaire_id=<?php echo $stats['active_questionnaire']['id']; ?>" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-question-circle me-1"></i>Manage Questions
                                </a>
                                <a href="../export_responses.php" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-download me-1"></i>Export Data
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No active questionnaire found. <a href="questionnaires.php" class="alert-link">Create or activate a questionnaire</a> to start collecting responses.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col">
                <h4 class="mb-3"><i class="fas fa-bolt me-2"></i>Quick Actions</h4>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="card quick-action-card">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-circle action-icon text-success"></i>
                        <h5>Create Questionnaire</h5>
                        <p class="text-muted">Create a new questionnaire with custom questions</p>
                        <a href="questionnaires.php?action=create" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>Create New
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card quick-action-card">
                    <div class="card-body text-center">
                        <i class="fas fa-list action-icon text-primary"></i>
                        <h5>Manage Questionnaires</h5>
                        <p class="text-muted">View, edit, and activate questionnaires</p>
                        <a href="questionnaires.php" class="btn btn-primary">
                            <i class="fas fa-cog me-1"></i>Manage
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card quick-action-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-bar action-icon text-info"></i>
                        <h5>View Reports</h5>
                        <p class="text-muted">Export and analyze response data</p>
                        <a href="../export_responses.php" class="btn btn-info">
                            <i class="fas fa-chart-line me-1"></i>View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
