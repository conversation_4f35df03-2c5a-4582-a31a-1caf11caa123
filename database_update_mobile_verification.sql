-- Database cleanup script for mobile verification feature
-- Run this if you previously added the mobile_verified column

USE ihcs_quest;

-- Remove mobile_verified column if it exists (since we use session-based verification now)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'ihcs_quest'
     AND TABLE_NAME = 'questionnaire_sessions'
     AND COLUMN_NAME = 'mobile_verified') > 0,
    'ALTER TABLE questionnaire_sessions DROP COLUMN mobile_verified',
    'SELECT "Column mobile_verified does not exist" as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show the updated table structure
DESCRIBE questionnaire_sessions;
