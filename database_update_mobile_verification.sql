-- Database update script to add mobile verification feature
-- Run this if you already have the questionnaire_sessions table

USE ihcs_quest;

-- Add mobile_verified column to existing questionnaire_sessions table
ALTER TABLE questionnaire_sessions 
ADD COLUMN mobile_verified BOOLEAN DEFAULT FALSE AFTER staff_name;

-- Update existing sessions to require mobile verification
-- (Set mobile_verified to FALSE for all existing records so they need to verify)
UPDATE questionnaire_sessions SET mobile_verified = FALSE;

-- Show the updated table structure
DESCRIBE questionnaire_sessions;
