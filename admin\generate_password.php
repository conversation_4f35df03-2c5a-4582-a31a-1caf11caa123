<?php
/**
 * Password Hash Generator
 * Run this script to generate password hashes for admin users
 */

// Generate hash for default password
$password = 'admin123';
$hash = password_hash($password, PASSWORD_DEFAULT);

echo "<h3>Password Hash Generator</h3>";
echo "<p><strong>Password:</strong> " . htmlspecialchars($password) . "</p>";
echo "<p><strong>Hash:</strong> " . htmlspecialchars($hash) . "</p>";

echo "<hr>";
echo "<h4>SQL to insert admin user:</h4>";
echo "<pre>";
echo "INSERT INTO admin_users (username, password_hash, full_name, email) VALUES\n";
echo "('admin', '" . $hash . "', 'System Administrator', '<EMAIL>');";
echo "</pre>";

echo "<hr>";
echo "<h4>Or update existing admin user:</h4>";
echo "<pre>";
echo "UPDATE admin_users SET password_hash = '" . $hash . "' WHERE username = 'admin';";
echo "</pre>";

// Test verification
echo "<hr>";
echo "<h4>Verification Test:</h4>";
if (password_verify($password, $hash)) {
    echo "<p style='color: green;'>✅ Password verification successful!</p>";
} else {
    echo "<p style='color: red;'>❌ Password verification failed!</p>";
}

echo "<hr>";
echo "<p><em>Copy the SQL statement above and run it in your database to set up the admin user.</em></p>";
?>
