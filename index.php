<?php
session_start();

$recaptcha_secret = "6Le32T0qAAAAAITvAkRCtUMpoknQmdIDI0yr-2cN";

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', 'laijingyue1984');
define('DB_NAME_QUEST', 'ihcs_quest');
define('DB_NAME_PAYS', 'ihcs_pays');

// Function to get questionnaire session
function getQuestionnaireSession($staffId) {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("SELECT * FROM questionnaire_sessions WHERE staff_id = :staffId LIMIT 1");
        $stmt->execute(['staffId' => $staffId]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        throw new Exception("Failed to get questionnaire session: " . $e->getMessage());
    }
}

// Function to create or update questionnaire session (removed mobile_verified from DB)
function createOrUpdateSession($staffId, $staffName, $consentGiven = false, $currentQuestion = 1) {
    try {
        $conn = getConnection(DB_NAME_QUEST);

        // Check if session exists
        $existing = getQuestionnaireSession($staffId);

        if ($existing) {
            // Update existing session
            $stmt = $conn->prepare("UPDATE questionnaire_sessions
                                   SET consent_given = :consent, current_question = :current, updated_at = NOW()
                                   WHERE staff_id = :staffId");
            $stmt->execute([
                'consent' => $consentGiven ? 1 : 0,
                'current' => $currentQuestion,
                'staffId' => $staffId
            ]);
        } else {
            // Create new session
            $stmt = $conn->prepare("INSERT INTO questionnaire_sessions
                                   (staff_id, staff_name, consent_given, current_question)
                                   VALUES (:staffId, :staffName, :consent, :current)");
            $stmt->execute([
                'staffId' => $staffId,
                'staffName' => $staffName,
                'consent' => $consentGiven ? 1 : 0,
                'current' => $currentQuestion
            ]);
        }

        return true;
    } catch(PDOException $e) {
        throw new Exception("Failed to create/update session: " . $e->getMessage());
    }
}

// Function to get database connection
function getConnection($dbName) {
    try {
        $conn = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . $dbName,
            DB_USER,
            DB_PASS
        );
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

// Function to get staff details from ihcs_pays database
function getStaffDetails($staffId) {
    try {
        $conn = getConnection(DB_NAME_PAYS);
        $stmt = $conn->prepare("SELECT fullname, mobile_phone_number FROM staff WHERE id = :staffId");
        $stmt->execute(['staffId' => $staffId]);

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result : null;
    } catch(PDOException $e) {
        throw new Exception("Failed to fetch staff details: " . $e->getMessage());
    }
}

// Function to get staff name from ihcs_pays database (backward compatibility)
function getStaffName($staffId) {
    $details = getStaffDetails($staffId);
    return $details ? $details['fullname'] : null;
}

// Function to verify mobile phone last 4 digits
function verifyMobilePhone($staffId, $inputDigits) {
    try {
        $details = getStaffDetails($staffId);
        if (!$details || !$details['mobile_phone_number']) {
            return false;
        }

        // Extract first mobile number (before semicolon if multiple numbers exist)
        $mobileNumberRaw = trim($details['mobile_phone_number']);
        $firstMobile = explode(';', $mobileNumberRaw)[0]; // Get first number before semicolon
        $firstMobile = trim($firstMobile); // Remove any whitespace

        // Extract last 4 digits from mobile phone number
        $mobileNumber = preg_replace('/[^0-9]/', '', $firstMobile); // Remove non-numeric characters
        $lastFourDigits = substr($mobileNumber, -4);

        return $lastFourDigits === $inputDigits;
    } catch(PDOException $e) {
        throw new Exception("Failed to verify mobile phone: " . $e->getMessage());
    }
}

// Function to check if mobile verification is valid for current session
function isMobileVerifiedInSession($staffId) {
    // Check if mobile verification exists in session and is not expired
    $sessionKey = 'mobile_verified_' . $staffId;
    $timeKey = 'mobile_verified_time_' . $staffId;

    if (!isset($_SESSION[$sessionKey]) || !isset($_SESSION[$timeKey])) {
        return false;
    }

    // Optional: Add expiration time (e.g., 30 minutes)
    $verificationTime = $_SESSION[$timeKey];
    $expirationTime = 30 * 60; // 30 minutes in seconds

    if ((time() - $verificationTime) > $expirationTime) {
        // Verification expired, remove from session
        unset($_SESSION[$sessionKey]);
        unset($_SESSION[$timeKey]);
        return false;
    }

    return $_SESSION[$sessionKey] === true;
}

// Function to get all questions
function getAllQuestions() {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("SELECT * FROM questionnaire_questions ORDER BY question_order");
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        throw new Exception("Failed to fetch questions: " . $e->getMessage());
    }
}

// Function to get a specific question by order
function getQuestionByOrder($order) {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("SELECT * FROM questionnaire_questions WHERE question_order = :order LIMIT 1");
        $stmt->execute(['order' => $order]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        throw new Exception("Failed to fetch question: " . $e->getMessage());
    }
}

// Function to get user's response for a question
function getUserResponse($staffId, $questionId) {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("SELECT response_text FROM questionnaire_responses
                               WHERE staff_id = :staffId AND question_id = :questionId LIMIT 1");
        $stmt->execute(['staffId' => $staffId, 'questionId' => $questionId]);

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['response_text'] : null;
    } catch(PDOException $e) {
        throw new Exception("Failed to fetch user response: " . $e->getMessage());
    }
}

// Function to save user response
function saveUserResponse($staffId, $staffName, $questionId, $response) {
    try {
        $conn = getConnection(DB_NAME_QUEST);

        // Use INSERT ... ON DUPLICATE KEY UPDATE to handle both new and existing responses
        $stmt = $conn->prepare("INSERT INTO questionnaire_responses
                               (staff_id, staff_name, question_id, response_text)
                               VALUES (:staffId, :staffName, :questionId, :response)
                               ON DUPLICATE KEY UPDATE
                               response_text = :response, updated_at = NOW()");

        return $stmt->execute([
            'staffId' => $staffId,
            'staffName' => $staffName,
            'questionId' => $questionId,
            'response' => $response
        ]);
    } catch(PDOException $e) {
        throw new Exception("Failed to save response: " . $e->getMessage());
    }
}

// Function to get total number of questions
function getTotalQuestions() {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM questionnaire_questions");
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    } catch(PDOException $e) {
        throw new Exception("Failed to get total questions: " . $e->getMessage());
    }
}

// Function to verify reCAPTCHA v3
function verifyRecaptcha($secret, $token) {
    $url = 'https://www.google.com/recaptcha/api/siteverify';
    $data = [
        'secret' => $secret,
        'response' => $token
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception("Failed to verify reCAPTCHA");
    }

    return json_decode($response, true);
}

// Function to mark questionnaire as completed
function markQuestionnaireCompleted($staffId) {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("UPDATE questionnaire_sessions
                               SET is_completed = 1, completed_at = NOW(), updated_at = NOW()
                               WHERE staff_id = :staffId");

        return $stmt->execute(['staffId' => $staffId]);
    } catch(PDOException $e) {
        throw new Exception("Failed to mark questionnaire as completed: " . $e->getMessage());
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        // Get and validate staff ID
        $staffId = trim($_POST['key'] ?? '');
        if (empty($staffId)) {
            throw new Exception("Something went wrong, please only follow the link provided in your email.");
        }

        // Get staff name from ihcs_pays database
        $staffName = getStaffName($staffId);
        if (!$staffName) {
            throw new Exception("Something went wrong, please contact office.");
        }

        if ($_POST['action'] === 'verify_mobile') {
            // Handle mobile phone verification
            $mobileDigits = trim($_POST['mobile_digits'] ?? '');

            if (empty($mobileDigits) || !preg_match('/^\d{4}$/', $mobileDigits)) {
                throw new Exception("Please enter exactly 4 digits");
            }

            if (verifyMobilePhone($staffId, $mobileDigits)) {
                // Mobile verified - store in PHP session (not database)
                $_SESSION['mobile_verified_' . $staffId] = true;
                $_SESSION['mobile_verified_time_' . $staffId] = time();

                echo json_encode([
                    'success' => true,
                    'message' => 'Mobile phone verified successfully',
                    'redirect' => true
                ]);
            } else {
                throw new Exception("The last 4 digits do not match our records. Please check your input and try again.");
            }

        } elseif ($_POST['action'] === 'give_consent') {
            // Handle consent submission
            if (!isset($_POST['token'])) {
                throw new Exception("reCAPTCHA verification failed");
            }

            // Verify reCAPTCHA
            $recaptcha_response = verifyRecaptcha($recaptcha_secret, $_POST['token']);

            if (!isset($recaptcha_response['success']) || !$recaptcha_response['success']) {
                throw new Exception("reCAPTCHA verification failed");
            }

            // Verify consent
            if (!isset($_POST['consent']) || $_POST['consent'] !== 'true') {
                throw new Exception("Consent is required");
            }

            // Create or update session with consent
            createOrUpdateSession($staffId, $staffName, true, 1);

            echo json_encode([
                'success' => true,
                'message' => 'Consent recorded. Redirecting to questionnaire...',
                'redirect' => true
            ]);

        } elseif ($_POST['action'] === 'save_response') {
            // Handle question response submission
            $questionOrder = intval($_POST['question_order'] ?? 0);
            $response = trim($_POST['response'] ?? '');
            $direction = $_POST['direction'] ?? 'next';

            if ($questionOrder <= 0) {
                throw new Exception("Invalid question");
            }

            // Get the question
            $question = getQuestionByOrder($questionOrder);
            if (!$question) {
                throw new Exception("Question not found");
            }

            // Validate required responses
            if ($question['is_required'] && empty($response)) {
                throw new Exception("This question is required");
            }

            // Save the response if provided
            if (!empty($response)) {
                saveUserResponse($staffId, $staffName, $question['id'], $response);
            }

            // Calculate next question
            $totalQuestions = getTotalQuestions();
            $nextQuestion = $questionOrder;

            if ($direction === 'next') {
                $nextQuestion = min($questionOrder + 1, $totalQuestions);
            } elseif ($direction === 'prev') {
                $nextQuestion = max($questionOrder - 1, 1);
            }

            // Update current question in session
            createOrUpdateSession($staffId, $staffName, true, $nextQuestion);

            // Check if questionnaire is completed
            if ($questionOrder == $totalQuestions && $direction === 'next') {
                // Check if this was already completed (update mode)
                $session = getQuestionnaireSession($staffId);
                $wasAlreadyCompleted = $session && $session['is_completed'];

                markQuestionnaireCompleted($staffId);

                $message = $wasAlreadyCompleted
                    ? 'Your responses have been updated successfully!'
                    : 'Thank you for completing the questionnaire!';

                echo json_encode([
                    'success' => true,
                    'completed' => true,
                    'message' => $message,
                    'was_update' => $wasAlreadyCompleted
                ]);
            } else {
                echo json_encode([
                    'success' => true,
                    'next_question' => $nextQuestion,
                    'total_questions' => $totalQuestions
                ]);
            }
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}
// Main page logic - determine what to show
$staffId = trim($_GET['key'] ?? '');
$showMobileVerification = false;
$showConsent = false;
$showQuestionnaire = false;
$showCompleted = false;
$currentQuestion = 1;
$totalQuestions = 0;
$question = null;
$userResponse = '';
$errorMessage = '';

if (empty($staffId)) {
    $errorMessage = "Invalid access. Please use the link provided in your email.";
} else {
    try {
        // Verify staff exists
        $staffName = getStaffName($staffId);
        if (!$staffName) {
            $errorMessage = "Invalid staff ID. Please contact office.";
        } else {
            // Check mobile verification in session first (required every visit)
            if (!isMobileVerifiedInSession($staffId)) {
                // Mobile not verified in current session - always show verification
                $showMobileVerification = true;
            } else {
                // Mobile verified in session - proceed with normal flow
                $session = getQuestionnaireSession($staffId);
                $totalQuestions = getTotalQuestions();

                if (!$session || !$session['consent_given']) {
                    // No session or no consent - show consent form
                    $showConsent = true;
                } else {
                    // Show questionnaire (whether completed or not - allow updates)
                    $showQuestionnaire = true;
                    $currentQuestion = $session['current_question'];

                    // If completed, start from question 1 to allow full review/update
                    if ($session['is_completed'] && $currentQuestion > $totalQuestions) {
                        $currentQuestion = 1;
                        // Update session to reset current question
                        createOrUpdateSession($staffId, $staffName, true, 1);
                    }

                    $question = getQuestionByOrder($currentQuestion);
                    if ($question) {
                        $userResponse = getUserResponse($staffId, $question['id']) ?? '';
                    }
                }
            }
        }
    } catch (Exception $e) {
        $errorMessage = "System error. Please try again later.";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IHCS Employee Benefits Program - Questionnaire</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Correct reCAPTCHA script inclusion -->
    <script src="https://www.google.com/recaptcha/api.js?render=6Le32T0qAAAAANJvxLWfn8BTO4OdsijBeoAotPao" async defer></script>
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 700px; }
        .card { margin-top: 50px; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .btn:disabled { cursor: not-allowed; }
        .progress { height: 10px; margin-bottom: 20px; }
        .question-text { font-size: 1.1em; font-weight: 500; margin-bottom: 20px; }
        .form-check-input { margin-right: 10px; }
        .navigation-buttons { margin-top: 30px; }
        textarea.form-control { min-height: 120px; }
        .logo-container { margin-bottom: 15px; }
        .logo-container img { max-height: 60px; width: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center">
                <div class="logo-container">
                    <img src="ihcslogo.svg" alt="IHCS Logo" class="img-fluid">
                </div>
                <h4 class="mb-0">Independent Health Care Service</h4>
                <h4 class="mb-0">Staff Questionnaire</h4>
            </div>
            <div class="card-body">
                <div id="messageArea"></div>

                <?php if ($errorMessage): ?>
                    <div class="alert alert-danger">
                        <strong>Error:</strong> <?php echo htmlspecialchars($errorMessage); ?>
                    </div>

                <?php elseif ($showMobileVerification): ?>
                    <!-- Mobile Verification Form -->
                    <div id="mobileVerificationSection">
                        <div class="text-center mb-4">
                            <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                            <h5>Mobile Phone Verification</h5>
                            <p class="text-muted">For security purposes, please complete your mobile phone number.</p>
                        </div>

                        <?php
                        // Get staff details to show masked mobile number
                        $staffDetails = getStaffDetails($staffId);
                        $maskedMobile = '';
                        if ($staffDetails && $staffDetails['mobile_phone_number']) {
                            // Extract first mobile number (before semicolon if multiple numbers exist)
                            $mobileNumberRaw = trim($staffDetails['mobile_phone_number']);
                            $firstMobile = explode(';', $mobileNumberRaw)[0]; // Get first number before semicolon
                            $firstMobile = trim($firstMobile); // Remove any whitespace

                            $mobileNumber = preg_replace('/[^0-9]/', '', $firstMobile);
                            if (strlen($mobileNumber) >= 4) {
                                // For Australian mobile: 0431234567 -> 0431 23xxxx (show first 6, mask last 4)
                                $visiblePart = substr($mobileNumber, 0, -4);
                                $maskedMobile = $visiblePart . 'xxxx';

                                // Format it nicely for 10-digit Australian mobile (e.g., 043123xxxx -> 0431 23xxxx)
                                if (strlen($mobileNumber) == 10) {
                                    $maskedMobile = substr($visiblePart, 0, 4) . ' ' . substr($visiblePart, 4) . 'xxxx';
                                }
                            }
                        }
                        ?>

                        <form id="mobileVerificationForm">
                            <input type="hidden" id="key" name="key" value="<?php echo htmlspecialchars($staffId); ?>">

                            <div class="mb-3">
                                <?php if ($maskedMobile): ?>
                                    <label for="mobileDigits" class="form-label">Complete your mobile phone number:</label>
                                    <div class="text-center mb-3">
                                        <div class="h5 text-primary"><?php echo htmlspecialchars($maskedMobile); ?></div>
                                        <small class="text-muted">Enter the last 4 digits to complete the number above</small>
                                    </div>
                                    <div class="input-group justify-content-center">
                                        <span class="input-group-text"><?php echo htmlspecialchars(substr($maskedMobile, 0, -4)); ?></span>
                                        <input type="text" class="form-control" id="mobileDigits" name="mobile_digits"
                                               maxlength="4" pattern="[0-9]{4}" placeholder="____"
                                               style="max-width: 100px; text-align: center; font-weight: bold;" required>
                                    </div>
                                <?php else: ?>
                                    <label for="mobileDigits" class="form-label">Enter the last 4 digits of your mobile phone number:</label>
                                    <div class="input-group justify-content-center">
                                        <span class="input-group-text">***-***-</span>
                                        <input type="text" class="form-control" id="mobileDigits" name="mobile_digits"
                                               maxlength="4" pattern="[0-9]{4}" placeholder="1234"
                                               style="max-width: 100px; text-align: center;" required>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary" id="verifyBtn">
                                    Verify Mobile Phone
                                </button>
                            </div>
                        </form>
                    </div>

                <?php elseif ($showConsent): ?>
                    <!-- Consent Form -->
                    <div id="consentSection">
                        <p class="mb-4">Welcome to the IHCS Employee Benefits Program questionnaire. Your participation is voluntary and your responses will be kept confidential.</p>

                        <form id="consentForm">
                            <input type="hidden" id="key" name="key" value="<?php echo htmlspecialchars($staffId); ?>">

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="consentCheck" required>
                                    <label class="form-check-label" for="consentCheck">
                                        I authorize Independent Health Care Services (IHCS) to collect and store securely my responses to this questionnaire. I understand that my participation is voluntary and my responses will be kept confidential.
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary" id="consentBtn" disabled>
                                I Agree - Start Questionnaire
                            </button>
                        </form>
                    </div>

                <?php elseif ($showCompleted): ?>
                    <!-- Completed Message -->
                    <div class="alert alert-success">
                        <h5>Thank you!</h5>
                        <p class="mb-0">You have already completed this questionnaire. If you need to make changes to your responses, please contact HR.</p>
                    </div>
                    <div class="text-center mt-3">
                        <a href="https://www.independenthealth.com.au" class="btn btn-outline-primary">Return to IHCS Website</a>
                    </div>

                <?php elseif ($showQuestionnaire && $question): ?>
                    <!-- Questionnaire -->
                    <div id="questionnaireSection">
                        <?php if ($session && $session['is_completed']): ?>
                            <!-- Show update message for completed questionnaires -->
                            <div class="alert alert-info mb-4">
                                <h6 class="mb-2"><i class="fas fa-info-circle"></i> Update Mode</h6>
                                <p class="mb-0">You have already completed this questionnaire. You can review and update your responses below. Navigate through the questions using the Previous/Next buttons.</p>
                            </div>
                        <?php endif; ?>

                        <!-- Progress Bar -->
                        <div class="progress">
                            <div class="progress-bar" role="progressbar"
                                 style="width: <?php echo ($currentQuestion / $totalQuestions) * 100; ?>%"
                                 aria-valuenow="<?php echo $currentQuestion; ?>"
                                 aria-valuemin="0"
                                 aria-valuemax="<?php echo $totalQuestions; ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">Question <?php echo $currentQuestion; ?> of <?php echo $totalQuestions; ?></small>
                            <?php if ($session && $session['is_completed']): ?>
                                <span class="badge bg-success ms-2">Previously Completed</span>
                            <?php endif; ?>
                        </div>

                        <form id="questionForm">
                            <input type="hidden" id="key" name="key" value="<?php echo htmlspecialchars($staffId); ?>">
                            <input type="hidden" id="questionOrder" name="question_order" value="<?php echo $currentQuestion; ?>">

                            <div class="question-text">
                                <?php echo htmlspecialchars($question['question_text']); ?>
                                <?php if ($question['is_required']): ?>
                                    <span class="text-danger">*</span>
                                <?php endif; ?>
                            </div>

                            <?php if ($question['question_type'] === 'multiple_choice'): ?>
                                <?php
                                $options = json_decode($question['options'], true);
                                foreach ($options as $option):
                                ?>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="response"
                                               id="option_<?php echo md5($option); ?>"
                                               value="<?php echo htmlspecialchars($option); ?>"
                                               <?php echo ($userResponse === $option) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="option_<?php echo md5($option); ?>">
                                            <?php echo htmlspecialchars($option); ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>

                            <?php elseif ($question['question_type'] === 'short_answer'): ?>
                                <input type="text" class="form-control" name="response"
                                       value="<?php echo htmlspecialchars($userResponse); ?>"
                                       placeholder="Enter your answer..."
                                       <?php echo $question['is_required'] ? 'required' : ''; ?>>

                            <?php elseif ($question['question_type'] === 'long_answer'): ?>
                                <textarea class="form-control" name="response"
                                          placeholder="Enter your detailed answer..."
                                          <?php echo $question['is_required'] ? 'required' : ''; ?>><?php echo htmlspecialchars($userResponse); ?></textarea>
                            <?php endif; ?>

                            <div class="navigation-buttons d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" id="prevBtn"
                                        <?php echo ($currentQuestion <= 1) ? 'disabled' : ''; ?>>
                                    Previous
                                </button>

                                <button type="submit" class="btn btn-primary" id="nextBtn">
                                    <?php echo ($currentQuestion >= $totalQuestions) ? 'Complete Questionnaire' : 'Next'; ?>
                                </button>
                            </div>
                        </form>
                    </div>

                <?php else: ?>
                    <div class="alert alert-warning">
                        <p class="mb-0">Unable to load questionnaire. Please try again later.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageArea = document.getElementById('messageArea');

    // Handle mobile verification form
    const mobileVerificationForm = document.getElementById('mobileVerificationForm');
    if (mobileVerificationForm) {
        const mobileDigits = document.getElementById('mobileDigits');
        const verifyBtn = document.getElementById('verifyBtn');

        // Only allow numeric input
        mobileDigits.addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        // Handle mobile verification form submission
        mobileVerificationForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const digits = mobileDigits.value.trim();
            if (digits.length !== 4) {
                showMessage('Please enter exactly 4 digits.', 'danger');
                return;
            }

            // Show loading state
            verifyBtn.disabled = true;
            verifyBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Verifying...';
            messageArea.innerHTML = '';

            const formData = new FormData();
            formData.append('action', 'verify_mobile');
            formData.append('key', document.getElementById('key').value);
            formData.append('mobile_digits', digits);

            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                if (data.success) {
                    showMessage(data.message, 'success');
                    if (data.redirect) {
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    }
                } else {
                    showMessage(data.message, 'danger');
                    resetVerifyButton();
                }
            })
            .catch(function(error) {
                showMessage('An error occurred. Please try again.', 'danger');
                resetVerifyButton();
            });
        });

        function resetVerifyButton() {
            verifyBtn.disabled = false;
            verifyBtn.innerHTML = 'Verify Mobile Phone';
        }
    }

    // Handle consent form
    const consentForm = document.getElementById('consentForm');
    if (consentForm) {
        const consentCheck = document.getElementById('consentCheck');
        const consentBtn = document.getElementById('consentBtn');

        // Enable/disable consent button based on checkbox
        consentCheck.addEventListener('change', function() {
            consentBtn.disabled = !this.checked;
        });

        // Handle consent form submission
        consentForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (!consentCheck.checked) {
                showMessage('Please check the consent box to continue.', 'danger');
                return;
            }

            // Show loading state
            consentBtn.disabled = true;
            consentBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Processing...';
            messageArea.innerHTML = '';

            // Make sure grecaptcha is loaded
            if (typeof grecaptcha === 'undefined') {
                showMessage('reCAPTCHA failed to load. Please refresh the page.', 'danger');
                resetConsentButton();
                return;
            }

            // Wait for reCAPTCHA to be ready
            grecaptcha.ready(function() {
                grecaptcha.execute('6Le32T0qAAAAANJvxLWfn8BTO4OdsijBeoAotPao', {action: 'consent'})
                    .then(function(token) {
                        const formData = new FormData();
                        formData.append('action', 'give_consent');
                        formData.append('key', document.getElementById('key').value);
                        formData.append('token', token);
                        formData.append('consent', 'true');

                        return fetch(window.location.href, {
                            method: 'POST',
                            body: formData
                        });
                    })
                    .then(function(response) {
                        return response.json();
                    })
                    .then(function(data) {
                        if (data.success) {
                            showMessage(data.message, 'success');
                            if (data.redirect) {
                                setTimeout(function() {
                                    window.location.reload();
                                }, 1500);
                            }
                        } else {
                            showMessage(data.message, 'danger');
                            resetConsentButton();
                        }
                    })
                    .catch(function(error) {
                        showMessage('An error occurred. Please try again.', 'danger');
                        resetConsentButton();
                    });
            });
        });

        function resetConsentButton() {
            consentBtn.disabled = !consentCheck.checked;
            consentBtn.innerHTML = 'I Agree - Start Questionnaire';
        }
    }

    // Handle questionnaire form
    const questionForm = document.getElementById('questionForm');
    if (questionForm) {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        // Handle previous button
        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                submitResponse('prev');
            });
        }

        // Handle form submission (next/complete)
        questionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitResponse('next');
        });

        function submitResponse(direction) {
            const formData = new FormData(questionForm);
            formData.append('action', 'save_response');
            formData.append('direction', direction);

            // Show loading state
            const isNext = direction === 'next';
            const button = isNext ? nextBtn : prevBtn;
            const originalText = button.innerHTML;

            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm"></span> ' +
                              (isNext ? 'Saving...' : 'Loading...');

            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                if (data.success) {
                    if (data.completed) {
                        showMessage(data.message, 'success');
                        // Hide form and show completion message
                        document.getElementById('questionnaireSection').style.display = 'none';

                        // Show different options based on whether this was an update or first completion
                        const additionalContent = data.was_update
                            ? `<div class="text-center mt-3">
                                <p class="text-muted mb-3">You can continue to make changes by refreshing this page, or return to the IHCS website.</p>
                                <button onclick="window.location.reload()" class="btn btn-secondary me-2">Make More Changes</button>
                                <a href="https://www.independenthealth.com.au" class="btn btn-outline-primary">Return to IHCS Website</a>
                               </div>`
                            : `<div class="text-center mt-3">
                                <p class="text-muted mb-3">Your responses have been saved. You can return to this page later to make changes if needed.</p>
                                <a href="https://www.independenthealth.com.au" class="btn btn-outline-primary">Return to IHCS Website</a>
                               </div>`;

                        messageArea.innerHTML += additionalContent;
                    } else {
                        // Reload page to show next/previous question
                        window.location.reload();
                    }
                } else {
                    showMessage(data.message, 'danger');
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            })
            .catch(function(error) {
                showMessage('An error occurred. Please try again.', 'danger');
                button.disabled = false;
                button.innerHTML = originalText;
            });
        }
    }

    function showMessage(message, type) {
        messageArea.innerHTML = `
            <div class="alert alert-${type} mb-3">
                ${message}
            </div>
        `;
        // Scroll to top to show message
        window.scrollTo(0, 0);
    }
});
</script>
</body>
</html>
