<?php
$recaptcha_secret = "6Le32T0qAAAAAITvAkRCtUMpoknQmdIDI0yr-2cN";

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', 'laijingyue1984');
define('DB_NAME_QUEST', 'ihcs_quest');
define('DB_NAME_PAYS', 'ihcs_pays');


function checkExistingConsent($staffId) {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("SELECT submission_datetime FROM ebp_consent WHERE staff_id = :staffId LIMIT 1");
        $stmt->execute(['staffId' => $staffId]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['submission_datetime'] : null;
    } catch(PDOException $e) {
        throw new Exception("Failed to check existing consent: " . $e->getMessage());
    }
}

// Function to get database connection
function getConnection($dbName) {
    try {
        $conn = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . $dbName, 
            DB_USER, 
            DB_PASS
        );
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

// Function to get staff name from ihcs_pays database
function getStaffName($staffId) {
    try {
        $conn = getConnection(DB_NAME_PAYS);
        $stmt = $conn->prepare("SELECT fullname FROM staff WHERE id = :staffId");
        $stmt->execute(['staffId' => $staffId]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['fullname'] : null;
    } catch(PDOException $e) {
        throw new Exception("Failed to fetch staff name: " . $e->getMessage());
    }
}

// Function to record consent in database
function recordConsent($staffId, $staffName) {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        
        // Insert the record
        $stmt = $conn->prepare("INSERT INTO ebp_consent (staff_id, staff_name, submission_datetime) 
                               VALUES (:staffId, :staffName, NOW())");
        
        return $stmt->execute([
            'staffId' => $staffId,
            'staffName' => $staffName
        ]);
    } catch(PDOException $e) {
        throw new Exception("Failed to record consent: " . $e->getMessage());
    }
}

// Function to verify reCAPTCHA v3
function verifyRecaptcha($secret, $token) {
    $url = 'https://www.google.com/recaptcha/api/siteverify';
    $data = [
        'secret' => $secret,
        'response' => $token
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception("Failed to verify reCAPTCHA");
    }

    return json_decode($response, true);
}

// Handle AJAX submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit') {
    header('Content-Type: application/json');
    
    try {
        if (!isset($_POST['token'])) {
            throw new Exception("reCAPTCHA verification failed");
        }

        // Verify reCAPTCHA
        $recaptcha_response = verifyRecaptcha($recaptcha_secret, $_POST['token']);
        
        if (!isset($recaptcha_response['success']) || !$recaptcha_response['success']) {
            throw new Exception("reCAPTCHA verification failed");
        }

        // Verify consent
        if (!isset($_POST['consent']) || $_POST['consent'] !== 'true') {
            throw new Exception("Consent is required");
        }

        // Get and validate staff ID
        $staffId = trim($_POST['key'] ?? '');
        if (empty($staffId)) {
            throw new Exception("Something went wrong, please only follow the link provided in your email.");
        }

        // Check for existing consent
        $existingConsent = checkExistingConsent($staffId);

        if ($existingConsent) {
            $formattedDate = date('d/m/Y H:i', strtotime($existingConsent));
            throw new Exception("You have already provided consent on {$formattedDate}. If you think this is an error, please contact HR.");
        }

        // Get staff name from ihcs_pays database
        $staffName = getStaffName($staffId);
        if (!$staffName) {
            throw new Exception("Something went wrong, please contact office.");
        }

        // Record the consent in database
        if (!recordConsent($staffId, $staffName)) {
            throw new Exception("Failed to record consent");
        }

        echo json_encode([
            'success' => true,
            'message' => 'Thank you for your consent. You may close this page or You will be redirected to our home page shortly.'
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IHCS Employee Benefits Program</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Correct reCAPTCHA script inclusion -->
    <script src="https://www.google.com/recaptcha/api.js?render=6Le32T0qAAAAANJvxLWfn8BTO4OdsijBeoAotPao" async defer></script>
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 600px; }
        .card { margin-top: 50px; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        #submitBtn:disabled { cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">IHCS Employee Benefits Program</h4>
            </div>
            <div class="card-body">
                <div id="messageArea"></div>
                
                <form id="consentForm">
                    <input type="hidden" id="key" name="key" value="<?php echo htmlspecialchars($_GET['key']); ?>">
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="consentCheck">
                            <label class="form-check-label" for="consentCheck">
                                I authorize Independent Health Care Services (IHCS) to collect my response and store securely my responses to this survey.
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" id="submitBtn" disabled>Submit</button>
                </form>
            </div>
        </div>
    </div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const consentCheck = document.getElementById('consentCheck');
    const submitBtn = document.getElementById('submitBtn');
    const form = document.getElementById('consentForm');
    const messageArea = document.getElementById('messageArea');

    // Enable/disable submit button based on consent
    consentCheck.addEventListener('change', function() {
        submitBtn.disabled = !this.checked;
    });

    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!consentCheck.checked) {
            messageArea.innerHTML = `
                <div class="alert alert-danger mb-3">Please authorize IHCS to share your information.</div>
            `;
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Processing...';
        messageArea.innerHTML = '';

        // Make sure grecaptcha is loaded
        if (typeof grecaptcha === 'undefined') {
            messageArea.innerHTML = `
                <div class="alert alert-danger">reCAPTCHA failed to load. Please refresh the page.</div>
            `;
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Submit';
            return;
        }

        // Wait for reCAPTCHA to be ready
        grecaptcha.ready(function() {
            grecaptcha.execute('6Le32T0qAAAAANJvxLWfn8BTO4OdsijBeoAotPao', {action: 'submit'})
                .then(function(token) {
                    const formData = new FormData();
                    formData.append('action', 'submit');
                    formData.append('key', document.getElementById('key').value);
                    formData.append('token', token);
                    formData.append('consent', 'true');

                    return fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    });
                })
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    if (data.success) {
                        messageArea.innerHTML = `
                            <div class="alert alert-success">${data.message}</div>
                        `;
                        // Disable form elements
                        submitBtn.style.display = 'none';
                        consentCheck.disabled = true;
                        
                        // Redirect after 3 seconds
                        setTimeout(function() {
                            window.location.href = 'https://www.independenthealth.com.au';
                        }, 3000);
                    } else {
                        messageArea.innerHTML = `
                            <div class="alert alert-danger">
                                <strong>Unable to process:</strong><br>
                                ${data.message}
                            </div>
                        `;
                        // If it's a duplicate consent, hide the form
                        if (data.message.includes('already provided consent')) {
                            submitBtn.style.display = 'none';
                            consentCheck.disabled = true;
                        } else {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = 'Submit';
                        }
                    }
                })
                .catch(function(error) {
                    messageArea.innerHTML = `
                        <div class="alert alert-danger">
                            An error occurred. Please try again.
                        </div>
                    `;
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = 'Submit';
                });
        });
    });
});
</script>
</body>
</html>
