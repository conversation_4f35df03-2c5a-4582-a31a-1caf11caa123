<?php
/**
 * Admin Authentication System
 */

session_start();

// Database configuration (same as main system)
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', 'laijingyue1984');
define('DB_NAME_QUEST', 'ihcs_quest');

// Function to get database connection
function getConnection($dbName) {
    try {
        $conn = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . $dbName, 
            DB_USER, 
            DB_PASS
        );
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

// Function to authenticate admin user
function authenticateAdmin($username, $password) {
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("SELECT id, username, password_hash, full_name, email FROM admin_users WHERE username = :username AND is_active = 1");
        $stmt->execute(['username' => $username]);
        
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($password, $user['password_hash'])) {
            return $user;
        }
        
        return false;
    } catch(PDOException $e) {
        throw new Exception("Authentication failed: " . $e->getMessage());
    }
}

// Function to check if user is logged in
function isAdminLoggedIn() {
    return isset($_SESSION['admin_user_id']) && isset($_SESSION['admin_username']);
}

// Function to require admin login
function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

// Function to get current admin user
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return null;
    }
    
    try {
        $conn = getConnection(DB_NAME_QUEST);
        $stmt = $conn->prepare("SELECT id, username, full_name, email FROM admin_users WHERE id = :id AND is_active = 1");
        $stmt->execute(['id' => $_SESSION['admin_user_id']]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        return null;
    }
}

// Function to logout admin
function logoutAdmin() {
    unset($_SESSION['admin_user_id']);
    unset($_SESSION['admin_username']);
    unset($_SESSION['admin_full_name']);
    session_destroy();
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'login') {
    header('Content-Type: application/json');
    
    try {
        $username = trim($_POST['username'] ?? '');
        $password = trim($_POST['password'] ?? '');
        
        if (empty($username) || empty($password)) {
            throw new Exception("Username and password are required");
        }
        
        $user = authenticateAdmin($username, $password);
        
        if ($user) {
            $_SESSION['admin_user_id'] = $user['id'];
            $_SESSION['admin_username'] = $user['username'];
            $_SESSION['admin_full_name'] = $user['full_name'];
            
            echo json_encode([
                'success' => true,
                'message' => 'Login successful',
                'redirect' => 'dashboard.php'
            ]);
        } else {
            throw new Exception("Invalid username or password");
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}

// Handle logout
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    logoutAdmin();
    header('Location: login.php?message=logged_out');
    exit;
}
?>
