<?php
/**
 * Admin Setup Script
 * Run this once to set up the admin user
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', 'laijingyue1984');
define('DB_NAME_QUEST', 'ihcs_quest');

function getConnection($dbName) {
    try {
        $conn = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . $dbName, 
            DB_USER, 
            DB_PASS
        );
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $username = trim($_POST['username'] ?? 'admin');
        $password = trim($_POST['password'] ?? '');
        $fullName = trim($_POST['full_name'] ?? 'System Administrator');
        $email = trim($_POST['email'] ?? '<EMAIL>');
        
        if (empty($password)) {
            throw new Exception("Password is required");
        }
        
        $conn = getConnection(DB_NAME_QUEST);
        
        // Check if admin_users table exists
        $stmt = $conn->prepare("SHOW TABLES LIKE 'admin_users'");
        $stmt->execute();
        if ($stmt->rowCount() == 0) {
            throw new Exception("admin_users table not found. Please run the database schema first.");
        }
        
        // Check if user already exists
        $stmt = $conn->prepare("SELECT id FROM admin_users WHERE username = :username");
        $stmt->execute(['username' => $username]);
        
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        if ($stmt->rowCount() > 0) {
            // Update existing user
            $stmt = $conn->prepare("UPDATE admin_users SET password_hash = :password_hash, full_name = :full_name, email = :email, updated_at = NOW() WHERE username = :username");
            $stmt->execute([
                'password_hash' => $passwordHash,
                'full_name' => $fullName,
                'email' => $email,
                'username' => $username
            ]);
            $message = "Admin user updated successfully!";
        } else {
            // Insert new user
            $stmt = $conn->prepare("INSERT INTO admin_users (username, password_hash, full_name, email) VALUES (:username, :password_hash, :full_name, :email)");
            $stmt->execute([
                'username' => $username,
                'password_hash' => $passwordHash,
                'full_name' => $fullName,
                'email' => $email
            ]);
            $message = "Admin user created successfully!";
        }
        
        $success = true;
        
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Setup - IHCS Questionnaire System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; padding: 50px 0; }
        .setup-container { max-width: 500px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 setup-container">
                <div class="card">
                    <div class="card-header text-center">
                        <h4>Admin User Setup</h4>
                        <p class="mb-0 text-muted">Create or update the admin user account</p>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?>">
                                <?php echo htmlspecialchars($message); ?>
                                <?php if ($success): ?>
                                    <hr>
                                    <a href="login.php" class="btn btn-primary btn-sm">Go to Login</a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="admin" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="Enter admin password" required>
                                <div class="form-text">Choose a strong password for security</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="full_name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="System Administrator" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<EMAIL>" required>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    Create/Update Admin User
                                </button>
                            </div>
                        </form>
                        
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">
                                Make sure you've run the database schema first:<br>
                                <code>source database_schema_admin.sql</code>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
