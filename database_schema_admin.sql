-- Enhanced database schema for generic questionnaire system with admin portal
-- Use the existing ihcs_quest database

USE ihcs_quest;

-- Table for admin users
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON>HAR(100) NOT NULL,
    email VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for questionnaires (multiple questionnaires support)
CREATE TABLE IF NOT EXISTS questionnaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT FALSE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_active (is_active)
);

-- Updated questions table to link to specific questionnaires
CREATE TABLE IF NOT EXISTS questionnaire_questions_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    questionnaire_id INT NOT NULL,
    question_order INT NOT NULL,
    question_text TEXT NOT NULL,
    question_type ENUM('multiple_choice', 'short_answer', 'long_answer') NOT NULL,
    options JSON NULL,
    is_required BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (questionnaire_id) REFERENCES questionnaires(id) ON DELETE CASCADE,
    UNIQUE KEY unique_questionnaire_order (questionnaire_id, question_order),
    INDEX idx_questionnaire_order (questionnaire_id, question_order)
);

-- Updated responses table to link to specific questionnaires
CREATE TABLE IF NOT EXISTS questionnaire_responses_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    questionnaire_id INT NOT NULL,
    staff_id VARCHAR(50) NOT NULL,
    staff_name VARCHAR(255) NOT NULL,
    question_id INT NOT NULL,
    response_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (questionnaire_id) REFERENCES questionnaires(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questionnaire_questions_new(id) ON DELETE CASCADE,
    UNIQUE KEY unique_staff_question (questionnaire_id, staff_id, question_id),
    INDEX idx_questionnaire_staff (questionnaire_id, staff_id)
);

-- Updated sessions table to link to specific questionnaires
CREATE TABLE IF NOT EXISTS questionnaire_sessions_new (
    id INT AUTO_INCREMENT PRIMARY KEY,
    questionnaire_id INT NOT NULL,
    staff_id VARCHAR(50) NOT NULL,
    staff_name VARCHAR(255) NOT NULL,
    consent_given BOOLEAN DEFAULT FALSE,
    current_question INT DEFAULT 1,
    is_completed BOOLEAN DEFAULT FALSE,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (questionnaire_id) REFERENCES questionnaires(id) ON DELETE CASCADE,
    UNIQUE KEY unique_questionnaire_staff (questionnaire_id, staff_id),
    INDEX idx_questionnaire_staff_session (questionnaire_id, staff_id)
);

-- Insert default admin user (password: admin123 - should be changed!)
INSERT INTO admin_users (username, password_hash, full_name, email) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', '<EMAIL>');

-- Create default questionnaire from existing data
INSERT INTO questionnaires (title, description, is_active, created_by) VALUES
('Staff Questionnaire 2025', 'Annual staff feedback questionnaire for IHCS employees', TRUE, 1);

-- Get the questionnaire ID for migration
SET @questionnaire_id = LAST_INSERT_ID();

-- Migrate existing questions to new structure
INSERT INTO questionnaire_questions_new (questionnaire_id, question_order, question_text, question_type, options, is_required)
SELECT @questionnaire_id, question_order, question_text, question_type, options, is_required
FROM questionnaire_questions
ORDER BY question_order;

-- Migrate existing responses to new structure
INSERT INTO questionnaire_responses_new (questionnaire_id, staff_id, staff_name, question_id, response_text, created_at, updated_at)
SELECT @questionnaire_id, r.staff_id, r.staff_name, 
       (SELECT nq.id FROM questionnaire_questions_new nq 
        JOIN questionnaire_questions oq ON nq.question_order = oq.question_order 
        WHERE oq.id = r.question_id AND nq.questionnaire_id = @questionnaire_id LIMIT 1),
       r.response_text, r.created_at, r.updated_at
FROM questionnaire_responses r
WHERE EXISTS (SELECT 1 FROM questionnaire_questions q WHERE q.id = r.question_id);

-- Migrate existing sessions to new structure
INSERT INTO questionnaire_sessions_new (questionnaire_id, staff_id, staff_name, consent_given, current_question, is_completed, started_at, completed_at, updated_at)
SELECT @questionnaire_id, staff_id, staff_name, consent_given, current_question, is_completed, started_at, completed_at, updated_at
FROM questionnaire_sessions;

-- Create backup tables and rename (uncomment when ready to migrate)
/*
RENAME TABLE questionnaire_questions TO questionnaire_questions_backup;
RENAME TABLE questionnaire_responses TO questionnaire_responses_backup;
RENAME TABLE questionnaire_sessions TO questionnaire_sessions_backup;

RENAME TABLE questionnaire_questions_new TO questionnaire_questions;
RENAME TABLE questionnaire_responses_new TO questionnaire_responses;
RENAME TABLE questionnaire_sessions_new TO questionnaire_sessions;
*/

-- Create indexes for better performance
CREATE INDEX idx_questionnaire_active ON questionnaires(is_active);
CREATE INDEX idx_admin_username ON admin_users(username);
CREATE INDEX idx_admin_active ON admin_users(is_active);
